import { AppDataSource } from '../config/database';
import { LotteryResult } from '../models/LotteryResult';
import { LotteryType } from '../models/LotteryType';

/**
 * 遗漏值预测器
 * 基于遗漏值频次分析的胆码预测算法
 */
export class MissValuePredictor {
  private lotteryTypeId: number;
  private positions: number;

  constructor(lotteryTypeId: number, positions: number) {
    this.lotteryTypeId = lotteryTypeId;
    this.positions = positions; // 3星、4星或5星
  }

  /**
   * 生成预测胆码
   */
  async generatePrediction(): Promise<PredictionResult> {
    console.log(`🎯 开始为彩种 ${this.lotteryTypeId} 生成胆码预测...`);

    try {
      // 1. 获取最近1152期数据（升序：最老到最新）
      const recentResults = await this.getRecentResults(1152);

      if (recentResults.length < 100) {
        console.warn(`⚠️  历史数据不足（${recentResults.length}期），无法生成可靠预测`);
        return { prediction: '' };
      }

      console.log(`📊 获取到 ${recentResults.length} 期历史数据`);

      // 2. 计算每期的遗漏值
      const periodsData = this.calculateAllMissValues(recentResults);

      // 3. 生成每个号码的遗漏值数组
      const missValueArrays = this.generateMissValueArrays(periodsData);

      // 4. 计算当前遗漏值
      const currentMissValues = this.calculateCurrentMissValues(recentResults);

      // 5. 统计当前遗漏值在历史数组中的个数
      const counts = this.countCurrentMissInArrays(missValueArrays, currentMissValues);

      // 6. 生成胆码组
      const { firstGroup, secondGroup } = this.generateDanmaGroups(currentMissValues, counts);

      // 7. 合并排序
      const combined = [...firstGroup, ...secondGroup].sort((a, b) => a - b);

      console.log(`🎯 第一组胆码（遗漏为0）:`, firstGroup);
      console.log(`🎯 第二组胆码（遗漏非0）:`, secondGroup);
      console.log(`🎯 合并胆码:`, combined);

      return {
        prediction: combined.join(',')
      };

    } catch (error) {
      console.error(`❌ 生成预测失败:`, error);
      return { prediction: '' };
    }
  }

  /**
   * 获取最近N期开奖结果（按期号升序）
   */
  private async getRecentResults(limit: number): Promise<LotteryResult[]> {
    // 先按时间降序获取最近N期，然后按期号升序排列
    const results = await AppDataSource.query(`
      SELECT id, lottery_type_id, period, draw_time, numbers
      FROM lottery_results
      WHERE lottery_type_id = ?
      ORDER BY draw_time DESC
      LIMIT ?
    `, [this.lotteryTypeId, limit]);

    // 将结果映射为LotteryResult对象
    const lotteryResults = results.map((row: any) => {
      const result = new LotteryResult();
      result.id = row.id;
      result.lotteryTypeId = row.lottery_type_id;
      result.period = row.period;
      result.drawTime = row.draw_time;
      result.numbers = row.numbers;
      return result;
    });

    // 按期号升序排列（从最老到最新）
    lotteryResults.sort((a: LotteryResult, b: LotteryResult) => {
      const periodA = parseInt(a.period);
      const periodB = parseInt(b.period);
      return periodA - periodB;
    });

    return lotteryResults;
  }

  /**
   * 计算所有期数的遗漏值
   */
  private calculateAllMissValues(results: LotteryResult[]): PeriodMissData[] {
    const periodsData: PeriodMissData[] = [];

    for (let i = 0; i < results.length; i++) {
      const currentResult = results[i];
      const missValues = new Array(10).fill(0);

      // 计算每个号码(0-9)在当前期的遗漏值
      for (let digit = 0; digit <= 9; digit++) {
        let missCount = 0;

        // 从当前期往前查找该数字上次出现的位置
        for (let j = i - 1; j >= 0; j--) {
          const prevResult = results[j];
          if (!prevResult) continue;

          const prevNumbers = this.parseNumbers(prevResult.numbers);

          if (prevNumbers.includes(digit)) {
            break; // 找到该数字，停止计数
          }

          missCount++;
        }

        missValues[digit] = missCount;
      }

      if (currentResult) {
        periodsData.push({
          period: currentResult.period,
          numbers: this.parseNumbers(currentResult.numbers),
          missValues: missValues
        });
      }
    }

    return periodsData;
  }

  /**
   * 生成每个号码的遗漏值数组
   * 记录每个号码开出时的上期遗漏值
   */
  private generateMissValueArrays(periodsData: PeriodMissData[]): Map<number, number[]> {
    const missValueArrays = new Map<number, number[]>();

    // 初始化每个号码的遗漏值数组
    for (let digit = 0; digit <= 9; digit++) {
      missValueArrays.set(digit, []);
    }

    // 从第二期开始处理（第一期没有上期遗漏值）
    for (let i = 1; i < periodsData.length; i++) {
      const currentPeriod = periodsData[i];
      const prevPeriod = periodsData[i - 1];

      if (!currentPeriod || !prevPeriod) continue;

      // 检查当前期开出的号码
      for (const digit of currentPeriod.numbers) {
        // 如果该号码在当前期开出，记录上期该号码的遗漏值
        const prevMissValue = prevPeriod.missValues[digit];
        if (prevMissValue !== undefined) {
          const missArray = missValueArrays.get(digit)!;
          missArray.push(prevMissValue);
        }
      }
    }

    // 输出调试信息
    for (let digit = 0; digit <= 9; digit++) {
      const array = missValueArrays.get(digit)!;
      console.log(`号码${digit}的遗漏值数组: [${array.join(',')}] (${array.length}个)`);
    }

    return missValueArrays;
  }

  /**
   * 计算当前每个号码的遗漏值
   */
  private calculateCurrentMissValues(results: LotteryResult[]): number[] {
    const currentMissValues = new Array(10).fill(0);

    for (let digit = 0; digit <= 9; digit++) {
      let missCount = 0;

      // 从最新期往前查找该数字上次出现的位置
      for (let i = results.length - 1; i >= 0; i--) {
        const result = results[i];
        if (!result) continue;

        const numbers = this.parseNumbers(result.numbers);

        if (numbers.includes(digit)) {
          break; // 找到该数字，停止计数
        }

        missCount++;
      }

      currentMissValues[digit] = missCount;
    }

    console.log(`当前遗漏值:`, currentMissValues.map((miss, digit) => `${digit}:${miss}`).join(', '));
    return currentMissValues;
  }

  /**
   * 统计当前遗漏值在历史数组中的个数
   */
  private countCurrentMissInArrays(missValueArrays: Map<number, number[]>, currentMissValues: number[]): number[] {
    const counts = new Array(10).fill(0);

    for (let digit = 0; digit <= 9; digit++) {
      const currentMiss = currentMissValues[digit];
      const missArray = missValueArrays.get(digit)!;

      // 统计当前遗漏值在数组中出现的次数
      const count = missArray.filter(value => value === currentMiss).length;
      counts[digit] = count;

      console.log(`号码${digit}当前遗漏${currentMiss}，遗漏值数组中有${currentMiss}，${count}个`);
    }

    return counts;
  }

  /**
   * 生成第一组和第二组胆码
   */
  private generateDanmaGroups(currentMissValues: number[], counts: number[]): {firstGroup: number[], secondGroup: number[]} {
    // 找出当前遗漏为0的号码
    const zeroMissDigits = [];
    for (let digit = 0; digit <= 9; digit++) {
      if (currentMissValues[digit] === 0) {
        zeroMissDigits.push(digit);
      }
    }

    // 第一组：在当前遗漏为0的号码中找出个数最多的
    let firstGroup = [];
    if (zeroMissDigits.length > 0) {
      let maxCount = -1;

      // 找出最大个数
      for (const digit of zeroMissDigits) {
        const count = counts[digit];
        if (count !== undefined && count > maxCount) {
          maxCount = count;
        }
      }

      // 找出所有达到最大个数的号码（处理并列第一）
      for (const digit of zeroMissDigits) {
        const count = counts[digit];
        if (count !== undefined && count === maxCount) {
          firstGroup.push(digit);
        }
      }
    }

    // 第二组：排除当前遗漏为0的号码，在剩下的号码中找出个数最多的
    const nonZeroMissDigits = [];
    for (let digit = 0; digit <= 9; digit++) {
      if (currentMissValues[digit] !== 0) {
        nonZeroMissDigits.push(digit);
      }
    }

    let secondGroup = [];
    if (nonZeroMissDigits.length > 0) {
      let maxCount = -1;

      // 找出最大个数
      for (const digit of nonZeroMissDigits) {
        const count = counts[digit];
        if (count !== undefined && count > maxCount) {
          maxCount = count;
        }
      }

      // 找出所有达到最大个数的号码（处理并列第一）
      for (const digit of nonZeroMissDigits) {
        const count = counts[digit];
        if (count !== undefined && count === maxCount) {
          secondGroup.push(digit);
        }
      }
    }

    return { firstGroup, secondGroup };
  }

  /**
   * 解析号码字符串为数字数组
   */
  private parseNumbers(numbersStr: string): number[] {
    if (numbersStr.includes(',')) {
      // 逗号分隔格式：如 "1,2,3"
      return numbersStr.split(',').map(n => parseInt(n.trim()));
    } else {
      // 连续数字格式：如 "123"
      return numbersStr.split('').map(n => parseInt(n));
    }
  }
}

/**
 * 预测结果接口
 */
export interface PredictionResult {
  prediction: string; // 预测胆码，如："1,2,5,7,8"
}

/**
 * 期数遗漏数据接口
 */
interface PeriodMissData {
  period: string;
  numbers: number[];
  missValues: number[]; // 每个号码(0-9)在该期的遗漏值
}
