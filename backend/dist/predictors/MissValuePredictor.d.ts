export declare class MissValuePredictor {
    private lotteryTypeId;
    private positions;
    constructor(lotteryTypeId: number, positions: number);
    generatePrediction(): Promise<PredictionResult>;
    private getRecentResults;
    private calculateAllMissValues;
    private generateMissValueArrays;
    private calculateCurrentMissValues;
    private countCurrentMissInArrays;
    private generateDanmaGroups;
    private parseNumbers;
}
export interface PredictionResult {
    prediction: string;
}
//# sourceMappingURL=MissValuePredictor.d.ts.map