"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
const database_1 = require("./config/database");
const collection_1 = __importDefault(require("./routes/collection"));
const prediction_1 = __importDefault(require("./routes/prediction"));
dotenv_1.default.config({ path: path_1.default.join(__dirname, '../../.env') });
const app = (0, express_1.default)();
const PORT = process.env.PORT || 3000;
app.use((0, helmet_1.default)());
app.use((0, cors_1.default)({
    origin: process.env.NODE_ENV === 'development' ? '*' : process.env.CORS_ORIGINS?.split(','),
    credentials: true
}));
app.use((0, morgan_1.default)('combined'));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true }));
app.use(express_1.default.static(path_1.default.join(__dirname, '../../frontend')));
app.get('/health', (req, res) => {
    res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        version: '1.0.0'
    });
});
app.get('/api/test', async (req, res) => {
    try {
        const isDbConnected = await (0, database_1.testDatabaseConnection)();
        res.json({
            success: true,
            message: '彩票分析平台API服务正常运行',
            database: isDbConnected ? '已连接' : '连接失败',
            timestamp: new Date().toISOString(),
            environment: process.env.NODE_ENV
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            message: '服务异常',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
});
app.get('/api/lottery/types', (req, res) => {
    const lotteryTypes = [
        {
            id: 'FC3D',
            name: '福彩3D',
            type: '三星彩',
            interval: 86400,
            nextDrawTime: '2025-01-06 21:15:00',
            latestPeriod: '2025005',
            latestResult: ['1', '2', '3'],
            latestDrawTime: '2025-01-05 21:15:00'
        },
        {
            id: 'PL3',
            name: '排列三',
            type: '三星彩',
            interval: 86400,
            nextDrawTime: '2025-01-06 21:30:00',
            latestPeriod: '2025005',
            latestResult: ['4', '5', '6'],
            latestDrawTime: '2025-01-05 21:30:00'
        },
        {
            id: 'PL4',
            name: '排列四',
            type: '四星彩',
            interval: 86400,
            nextDrawTime: '2025-01-06 21:30:00',
            latestPeriod: '2025005',
            latestResult: ['7', '8', '9', '0'],
            latestDrawTime: '2025-01-05 21:30:00'
        },
        {
            id: 'PC28',
            name: '加拿大PC28',
            type: '高频彩',
            interval: 210,
            nextDrawTime: new Date(Date.now() + 180000).toISOString(),
            latestPeriod: '20250105001',
            latestResult: ['1', '2'],
            latestDrawTime: new Date(Date.now() - 30000).toISOString()
        },
        {
            id: 'XYWXC',
            name: '幸运五星彩前四',
            type: '高频彩',
            interval: 300,
            nextDrawTime: new Date(Date.now() + 240000).toISOString(),
            latestPeriod: '20250105001',
            latestResult: ['3', '4', '5', '6'],
            latestDrawTime: new Date(Date.now() - 60000).toISOString()
        }
    ];
    res.json({
        success: true,
        data: lotteryTypes
    });
});
app.use('/api/collection', collection_1.default);
app.use('/api/prediction', prediction_1.default);
app.get('/api/lottery/:id/info', (req, res) => {
    const { id } = req.params;
    const mockData = {
        'FC3D': {
            id: 'FC3D',
            name: '福彩3D',
            type: '三星彩',
            interval: 86400,
            nextDrawTime: '2025-01-06 21:15:00',
            latestPeriod: '2025005',
            latestResult: ['1', '2', '3'],
            latestDrawTime: '2025-01-05 21:15:00'
        },
        'PL3': {
            id: 'PL3',
            name: '排列三',
            type: '三星彩',
            interval: 86400,
            nextDrawTime: '2025-01-06 21:30:00',
            latestPeriod: '2025005',
            latestResult: ['4', '5', '6'],
            latestDrawTime: '2025-01-05 21:30:00'
        }
    };
    const data = mockData[id];
    if (!data) {
        return res.status(404).json({
            success: false,
            message: '彩种不存在'
        });
    }
    return res.json({
        success: true,
        data
    });
});
app.use('*', (req, res) => {
    if (req.originalUrl.startsWith('/api/')) {
        res.status(404).json({
            success: false,
            message: 'API接口不存在'
        });
    }
    else {
        res.sendFile(path_1.default.join(__dirname, '../../frontend/user/index.html'));
    }
});
app.use((err, req, res, next) => {
    console.error('服务器错误:', err);
    res.status(500).json({
        success: false,
        message: process.env.NODE_ENV === 'development' ? err.message : '服务器内部错误',
        ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
    });
});
async function startServer() {
    try {
        console.log('🔄 正在初始化数据库连接...');
        await database_1.AppDataSource.initialize();
        console.log('✅ 数据库连接成功');
        app.listen(PORT, () => {
            console.log(`🚀 彩票分析平台后端服务启动成功`);
            console.log(`📍 服务地址: http://localhost:${PORT}`);
            console.log(`🌍 运行环境: ${process.env.NODE_ENV}`);
            console.log(`⏰ 启动时间: ${new Date().toLocaleString('zh-CN')}`);
            if (process.env.NODE_ENV === 'development') {
                console.log(`\n🔧 开发模式功能:`);
                console.log(`   - API测试: http://localhost:${PORT}/api/test`);
                console.log(`   - 健康检查: http://localhost:${PORT}/health`);
                console.log(`   - 彩种列表: http://localhost:${PORT}/api/lottery/types`);
                console.log(`   - 前端页面: http://localhost:${PORT}`);
            }
        });
    }
    catch (error) {
        console.error('❌ 服务器启动失败:', error);
        process.exit(1);
    }
}
startServer();
exports.default = app;
//# sourceMappingURL=app.js.map