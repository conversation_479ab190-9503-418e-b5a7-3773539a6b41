"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.testPrediction = testPrediction;
exports.testDetailedPrediction = testDetailedPrediction;
const database_1 = require("../config/database");
const MissValuePredictor_1 = require("../predictors/MissValuePredictor");
const PredictionService_1 = require("../services/PredictionService");
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
dotenv_1.default.config({ path: path_1.default.join(__dirname, '../../../.env') });
async function testPrediction() {
    try {
        console.log('🔄 初始化数据库连接...');
        await database_1.AppDataSource.initialize();
        console.log('✅ 数据库连接成功');
        const lotteryTypes = await database_1.AppDataSource.query(`
      SELECT id, code, name, type, status
      FROM lottery_types
      WHERE status = 'active'
      ORDER BY id
    `);
        console.log(`📊 找到 ${lotteryTypes.length} 个启用的彩种:`);
        lotteryTypes.forEach((lt) => {
            console.log(`   - ${lt.name} (${lt.code}) - ${lt.type}`);
        });
        if (lotteryTypes.length === 0) {
            console.log('⚠️  没有找到启用的彩种，请先初始化数据库');
            return;
        }
        for (const lotteryType of lotteryTypes) {
            console.log(`\n🎯 测试 ${lotteryType.name} 的胆码预测...`);
            const dataCount = await database_1.AppDataSource.query(`
        SELECT COUNT(*) as count
        FROM lottery_results
        WHERE lottery_type_id = ?
      `, [lotteryType.id]);
            const count = dataCount[0].count;
            console.log(`📊 ${lotteryType.name} 历史数据量: ${count} 期`);
            if (count < 10) {
                console.log(`⚠️  ${lotteryType.name} 历史数据不足，跳过预测测试`);
                continue;
            }
            const positions = getPositionsByType(lotteryType.type);
            const predictor = new MissValuePredictor_1.MissValuePredictor(lotteryType.id, positions);
            const result = await predictor.generatePrediction();
            if (result.prediction) {
                console.log(`✅ ${lotteryType.name} 预测成功: ${result.prediction}`);
                const predictionService = PredictionService_1.PredictionService.getInstance();
                await predictionService.generatePredictionForLottery(lotteryType.id);
                const savedPrediction = await predictionService.getLatestPrediction(lotteryType.id);
                if (savedPrediction) {
                    console.log(`💾 ${lotteryType.name} 预测已保存: 期号 ${savedPrediction.target_period}, 胆码 ${savedPrediction.prediction}`);
                }
            }
            else {
                console.log(`❌ ${lotteryType.name} 预测失败或结果为空`);
            }
        }
        console.log(`\n🎯 测试批量预测功能...`);
        const predictionService = PredictionService_1.PredictionService.getInstance();
        await predictionService.generatePredictionsForAllLotteries();
        console.log(`\n📊 获取预测统计信息...`);
        const stats = await predictionService.getPredictionStats();
        console.log(`总预测数: ${stats.total}`);
        console.log(`今日预测数: ${stats.today}`);
        console.log(`各彩种预测情况:`);
        stats.byLottery.forEach((item) => {
            console.log(`   - ${item.lottery_name}: ${item.prediction_count} 条预测, 最新: ${item.latest_prediction || '无'}`);
        });
        console.log(`\n✅ 胆码预测功能测试完成`);
    }
    catch (error) {
        console.error('❌ 测试失败:', error);
    }
    finally {
        if (database_1.AppDataSource.isInitialized) {
            await database_1.AppDataSource.destroy();
            console.log('🔄 数据库连接已关闭');
        }
    }
}
function getPositionsByType(type) {
    switch (type) {
        case '3star':
            return 3;
        case '4star':
            return 4;
        case '5star':
            return 5;
        default:
            return 3;
    }
}
async function testDetailedPrediction(lotteryTypeId) {
    console.log(`\n🔍 详细测试彩种 ${lotteryTypeId} 的预测算法...`);
    try {
        const recentResults = await database_1.AppDataSource.query(`
      SELECT period, draw_time, numbers
      FROM lottery_results
      WHERE lottery_type_id = ?
      ORDER BY draw_time DESC
      LIMIT 10
    `, [lotteryTypeId]);
        console.log(`📊 最近10期开奖数据:`);
        recentResults.reverse().forEach((result, index) => {
            console.log(`   ${index + 1}. 期号: ${result.period}, 号码: ${result.numbers}, 时间: ${result.draw_time}`);
        });
        const predictor = new MissValuePredictor_1.MissValuePredictor(lotteryTypeId, 3);
        const result = await predictor.generatePrediction();
        console.log(`🎯 预测结果: ${result.prediction}`);
    }
    catch (error) {
        console.error('❌ 详细测试失败:', error);
    }
}
async function main() {
    const args = process.argv.slice(2);
    if (args.length > 0 && args[0] === 'detail') {
        const lotteryTypeId = parseInt(args[1] || '1');
        console.log('🔄 初始化数据库连接...');
        await database_1.AppDataSource.initialize();
        console.log('✅ 数据库连接成功');
        await testDetailedPrediction(lotteryTypeId);
        if (database_1.AppDataSource.isInitialized) {
            await database_1.AppDataSource.destroy();
        }
    }
    else {
        await testPrediction();
    }
}
if (require.main === module) {
    main().catch(console.error);
}
//# sourceMappingURL=testPrediction.js.map