"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.testDataOrder = testDataOrder;
exports.testDirectDataFetch = testDirectDataFetch;
const database_1 = require("../config/database");
const MissValuePredictor_1 = require("../predictors/MissValuePredictor");
const dotenv_1 = __importDefault(require("dotenv"));
const path_1 = __importDefault(require("path"));
dotenv_1.default.config({ path: path_1.default.join(__dirname, '../../../.env') });
async function testDataOrder() {
    try {
        console.log('🔄 初始化数据库连接...');
        await database_1.AppDataSource.initialize();
        console.log('✅ 数据库连接成功');
        const lotteryTypes = await database_1.AppDataSource.query(`
      SELECT id, code, name
      FROM lottery_types
      WHERE code = 'PL3'
    `);
        if (lotteryTypes.length === 0) {
            console.log('❌ 未找到排列三彩种');
            return;
        }
        const pl3LotteryType = lotteryTypes[0];
        console.log(`📊 测试彩种: ${pl3LotteryType.name} (ID: ${pl3LotteryType.id})`);
        const predictor = new MissValuePredictor_1.MissValuePredictor(pl3LotteryType.id, 3);
        const getRecentResults = predictor.getRecentResults.bind(predictor);
        console.log('\n🔍 测试获取最近16期数据...');
        console.log('\n📋 数据库中最近16期数据（按时间降序）:');
        const dbResults = await database_1.AppDataSource.query(`
      SELECT period, numbers, draw_time
      FROM lottery_results
      WHERE lottery_type_id = ?
      ORDER BY draw_time DESC
      LIMIT 16
    `, [pl3LotteryType.id]);
        dbResults.forEach((row, index) => {
            console.log(`   ${index + 1}. 期号: ${row.period}, 号码: ${row.numbers}, 时间: ${row.draw_time}`);
        });
        console.log('\n📋 数据库中最近16期数据（按期号升序）:');
        const dbResultsSorted = [...dbResults].sort((a, b) => {
            return parseInt(a.period) - parseInt(b.period);
        });
        dbResultsSorted.forEach((row, index) => {
            console.log(`   ${index + 1}. 期号: ${row.period}, 号码: ${row.numbers}, 时间: ${row.draw_time}`);
        });
        console.log('\n🎯 预测器获取的数据顺序:');
        try {
            console.log('\n🧮 开始测试预测算法...');
            const result = await predictor.generatePrediction();
            console.log(`✅ 预测结果: ${result.prediction}`);
        }
        catch (error) {
            console.error('❌ 测试预测算法失败:', error);
        }
    }
    catch (error) {
        console.error('❌ 测试失败:', error);
    }
    finally {
        if (database_1.AppDataSource.isInitialized) {
            await database_1.AppDataSource.destroy();
            console.log('🔄 数据库连接已关闭');
        }
    }
}
async function testDirectDataFetch() {
    try {
        console.log('\n🔍 直接测试数据获取逻辑...');
        await database_1.AppDataSource.initialize();
        const results = await database_1.AppDataSource.query(`
      SELECT id, lottery_type_id, period, draw_time, numbers
      FROM lottery_results
      WHERE lottery_type_id = (SELECT id FROM lottery_types WHERE code = 'PL3')
      ORDER BY draw_time DESC
      LIMIT 16
    `);
        console.log('\n📊 获取到的数据（按时间降序）:');
        results.forEach((row, index) => {
            console.log(`   ${index + 1}. 期号: ${row.period}, 号码: ${row.numbers}, 时间: ${row.draw_time}`);
        });
        results.sort((a, b) => {
            const periodA = parseInt(a.period);
            const periodB = parseInt(b.period);
            return periodA - periodB;
        });
        console.log('\n📊 排序后的数据（按期号升序）:');
        results.forEach((row, index) => {
            console.log(`   ${index + 1}. 期号: ${row.period}, 号码: ${row.numbers}, 时间: ${row.draw_time}`);
        });
        console.log('\n✅ 数据获取逻辑测试完成');
    }
    catch (error) {
        console.error('❌ 直接测试失败:', error);
    }
    finally {
        if (database_1.AppDataSource.isInitialized) {
            await database_1.AppDataSource.destroy();
        }
    }
}
async function main() {
    console.log('🧪 开始测试数据获取顺序...\n');
    await testDirectDataFetch();
    await testDataOrder();
}
if (require.main === module) {
    main().catch(console.error);
}
//# sourceMappingURL=testDataOrder.js.map