"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PredictionController = void 0;
const PredictionService_1 = require("../services/PredictionService");
const database_1 = require("../config/database");
class PredictionController {
    constructor() {
        this.predictionService = PredictionService_1.PredictionService.getInstance();
    }
    async getLatestPrediction(req, res) {
        try {
            const lotteryTypeId = parseInt(req.params.lotteryId || '0');
            if (isNaN(lotteryTypeId)) {
                res.status(400).json({
                    success: false,
                    message: '无效的彩种ID'
                });
                return;
            }
            const prediction = await this.predictionService.getLatestPrediction(lotteryTypeId);
            if (!prediction) {
                res.json({
                    success: true,
                    data: null,
                    message: '暂无预测数据'
                });
                return;
            }
            const formattedPrediction = {
                targetPeriod: prediction.target_period,
                predictions: prediction.prediction.split(','),
                predictionsDisplay: prediction.prediction.split(',').join(' '),
                createdAt: prediction.created_at
            };
            res.json({
                success: true,
                data: formattedPrediction
            });
        }
        catch (error) {
            console.error('获取最新预测失败:', error);
            res.status(500).json({
                success: false,
                message: '获取预测数据失败'
            });
        }
    }
    async getHistoryPredictions(req, res) {
        try {
            const lotteryTypeId = parseInt(req.params.lotteryId || '0');
            const limit = parseInt(req.query.limit) || 10;
            if (isNaN(lotteryTypeId)) {
                res.status(400).json({
                    success: false,
                    message: '无效的彩种ID'
                });
                return;
            }
            const predictions = await this.predictionService.getHistoryPredictions(lotteryTypeId, limit);
            const formattedPredictions = predictions.map(prediction => ({
                targetPeriod: prediction.target_period,
                predictions: prediction.prediction.split(','),
                predictionsDisplay: prediction.prediction.split(',').join(' '),
                createdAt: prediction.created_at
            }));
            res.json({
                success: true,
                data: formattedPredictions
            });
        }
        catch (error) {
            console.error('获取历史预测失败:', error);
            res.status(500).json({
                success: false,
                message: '获取历史预测失败'
            });
        }
    }
    async generatePrediction(req, res) {
        try {
            const lotteryTypeId = parseInt(req.params.lotteryId || '0');
            if (isNaN(lotteryTypeId)) {
                res.status(400).json({
                    success: false,
                    message: '无效的彩种ID'
                });
                return;
            }
            const lotteryType = await this.getLotteryType(lotteryTypeId);
            if (!lotteryType) {
                res.status(404).json({
                    success: false,
                    message: '彩种不存在'
                });
                return;
            }
            await this.predictionService.generatePredictionForLottery(lotteryTypeId);
            const prediction = await this.predictionService.getLatestPrediction(lotteryTypeId);
            res.json({
                success: true,
                message: '预测生成成功',
                data: prediction ? {
                    targetPeriod: prediction.target_period,
                    predictions: prediction.prediction.split(','),
                    predictionsDisplay: prediction.prediction.split(',').join(' '),
                    createdAt: prediction.created_at
                } : null
            });
        }
        catch (error) {
            console.error('生成预测失败:', error);
            res.status(500).json({
                success: false,
                message: '生成预测失败'
            });
        }
    }
    async generateAllPredictions(req, res) {
        try {
            await this.predictionService.generatePredictionsForAllLotteries();
            res.json({
                success: true,
                message: '批量预测生成完成'
            });
        }
        catch (error) {
            console.error('批量生成预测失败:', error);
            res.status(500).json({
                success: false,
                message: '批量生成预测失败'
            });
        }
    }
    async getPredictionStats(req, res) {
        try {
            const stats = await this.predictionService.getPredictionStats();
            res.json({
                success: true,
                data: stats
            });
        }
        catch (error) {
            console.error('获取预测统计失败:', error);
            res.status(500).json({
                success: false,
                message: '获取预测统计失败'
            });
        }
    }
    async cleanupPredictions(req, res) {
        try {
            const daysToKeep = parseInt(req.body.daysToKeep) || 30;
            await this.predictionService.cleanupOldPredictions(daysToKeep);
            res.json({
                success: true,
                message: `清理完成，保留最近 ${daysToKeep} 天的预测数据`
            });
        }
        catch (error) {
            console.error('清理预测数据失败:', error);
            res.status(500).json({
                success: false,
                message: '清理预测数据失败'
            });
        }
    }
    async getLotteryType(lotteryTypeId) {
        try {
            const result = await database_1.AppDataSource.query(`
        SELECT id, code, name, type, status
        FROM lottery_types
        WHERE id = ?
      `, [lotteryTypeId]);
            return result.length > 0 ? result[0] : null;
        }
        catch (error) {
            console.error('获取彩种信息失败:', error);
            return null;
        }
    }
    async getPredictionComparison(req, res) {
        try {
            const lotteryTypeId = parseInt(req.params.lotteryId || '0');
            const targetPeriod = req.params.period;
            if (isNaN(lotteryTypeId) || !targetPeriod) {
                res.status(400).json({
                    success: false,
                    message: '参数错误'
                });
                return;
            }
            const predictionResult = await database_1.AppDataSource.query(`
        SELECT prediction, created_at
        FROM predictions
        WHERE lottery_type_id = ? AND target_period = ?
      `, [lotteryTypeId, targetPeriod]);
            if (predictionResult.length === 0) {
                res.status(404).json({
                    success: false,
                    message: '未找到预测数据'
                });
                return;
            }
            const lotteryResult = await database_1.AppDataSource.query(`
        SELECT numbers, draw_time
        FROM lottery_results
        WHERE lottery_type_id = ? AND period = ?
      `, [lotteryTypeId, targetPeriod]);
            const prediction = predictionResult[0];
            const predictedNumbers = prediction.prediction.split(',');
            let comparison = {
                targetPeriod,
                predictions: predictedNumbers,
                predictionsDisplay: predictedNumbers.join(' '),
                predictionCreatedAt: prediction.created_at,
                actualNumbers: null,
                actualNumbersDisplay: null,
                drawTime: null,
                hits: [],
                hitCount: 0,
                hitRate: 0,
                isDrawn: false
            };
            if (lotteryResult.length > 0) {
                const actual = lotteryResult[0];
                const actualNumbers = actual.numbers.includes(',')
                    ? actual.numbers.split(',')
                    : actual.numbers.split('');
                const hits = predictedNumbers.filter((pred) => actualNumbers.includes(pred));
                comparison = {
                    ...comparison,
                    actualNumbers,
                    actualNumbersDisplay: actualNumbers.join(' '),
                    drawTime: actual.draw_time,
                    hits,
                    hitCount: hits.length,
                    hitRate: predictedNumbers.length > 0 ? hits.length / predictedNumbers.length : 0,
                    isDrawn: true
                };
            }
            res.json({
                success: true,
                data: comparison
            });
        }
        catch (error) {
            console.error('获取预测对比失败:', error);
            res.status(500).json({
                success: false,
                message: '获取预测对比失败'
            });
        }
    }
}
exports.PredictionController = PredictionController;
//# sourceMappingURL=PredictionController.js.map