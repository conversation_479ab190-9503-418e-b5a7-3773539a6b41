import { Request, Response } from 'express';
export declare class PredictionController {
    private predictionService;
    constructor();
    getLatestPrediction(req: Request, res: Response): Promise<void>;
    getHistoryPredictions(req: Request, res: Response): Promise<void>;
    generatePrediction(req: Request, res: Response): Promise<void>;
    generateAllPredictions(req: Request, res: Response): Promise<void>;
    getPredictionStats(req: Request, res: Response): Promise<void>;
    cleanupPredictions(req: Request, res: Response): Promise<void>;
    private getLotteryType;
    getPredictionComparison(req: Request, res: Response): Promise<void>;
}
//# sourceMappingURL=PredictionController.d.ts.map