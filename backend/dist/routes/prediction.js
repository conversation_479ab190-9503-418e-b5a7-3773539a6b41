"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const PredictionController_1 = require("../controllers/PredictionController");
const router = (0, express_1.Router)();
const predictionController = new PredictionController_1.PredictionController();
router.get('/lottery/:lotteryId/latest', predictionController.getLatestPrediction.bind(predictionController));
router.get('/lottery/:lotteryId/history', predictionController.getHistoryPredictions.bind(predictionController));
router.get('/lottery/:lotteryId/period/:period/comparison', predictionController.getPredictionComparison.bind(predictionController));
router.post('/lottery/:lotteryId/generate', predictionController.generatePrediction.bind(predictionController));
router.post('/generate-all', predictionController.generateAllPredictions.bind(predictionController));
router.get('/stats', predictionController.getPredictionStats.bind(predictionController));
router.post('/cleanup', predictionController.cleanupPredictions.bind(predictionController));
exports.default = router;
//# sourceMappingURL=prediction.js.map