export declare class DataCollectionService {
    private static instance;
    private isRunning;
    private intervals;
    private constructor();
    static getInstance(): DataCollectionService;
    start(): Promise<void>;
    stop(): void;
    private getActiveLotteryTypes;
    private startCollectionForLotteryType;
    private collectDataForLotteryType;
    private collectFromTxtFile;
    private collectFromApi;
    private parseTxtContent;
    private parseTxtLine;
    private parseApiResponse;
    private parsePC28ApiResponse;
    private parseXYXCApiResponse;
    private saveCollectedData;
    private triggerPrediction;
    manualCollect(lotteryCode: string): Promise<void>;
    private ensurePC28HistoricalData;
    private batchCollectPC28HistoricalData;
    private ensureXYXCHistoricalData;
    private batchCollectXYXCHistoricalData;
    private fetchPC28HistoricalDataForDate;
    private fetchXYXCHistoricalDataForDate;
    private parsePC28HistoricalData;
    private parseXYXCHistoricalData;
    private deduplicateByPeriod;
    private batchSaveHistoricalData;
    initializeLotteryData(lotteryCode: string): Promise<void>;
    private shouldCollectNow;
    updateLotteryConfig(lotteryCode: string, config: {
        collectInterval?: number;
        dataSourceUrl?: string;
        collectWindowMinutes?: number;
    }): Promise<void>;
    private restartCollectionForLottery;
    getStatus(): any;
}
//# sourceMappingURL=DataCollectionService.d.ts.map