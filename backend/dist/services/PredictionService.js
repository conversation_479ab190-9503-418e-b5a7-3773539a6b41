"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PredictionService = void 0;
const database_1 = require("../config/database");
const MissValuePredictor_1 = require("../predictors/MissValuePredictor");
class PredictionService {
    constructor() { }
    static getInstance() {
        if (!PredictionService.instance) {
            PredictionService.instance = new PredictionService();
        }
        return PredictionService.instance;
    }
    async generatePredictionForLottery(lotteryTypeId) {
        try {
            console.log(`🎯 开始为彩种 ${lotteryTypeId} 生成预测...`);
            const lotteryType = await this.getLotteryType(lotteryTypeId);
            if (!lotteryType) {
                console.error(`❌ 彩种 ${lotteryTypeId} 不存在`);
                return;
            }
            if (lotteryType.status !== 'active') {
                console.log(`⚠️  彩种 ${lotteryType.name} 未启用，跳过预测`);
                return;
            }
            const latestResult = await this.getLatestResult(lotteryTypeId);
            if (!latestResult) {
                console.log(`⚠️  彩种 ${lotteryType.name} 暂无开奖数据，跳过预测`);
                return;
            }
            const nextPeriod = this.calculateNextPeriod(latestResult.period, lotteryType);
            const existingPrediction = await this.getPrediction(lotteryTypeId, nextPeriod);
            if (existingPrediction) {
                console.log(`ℹ️  期号 ${nextPeriod} 已有预测，跳过生成`);
                return;
            }
            const positions = this.getPositionsByType(lotteryType.type);
            const predictor = new MissValuePredictor_1.MissValuePredictor(lotteryTypeId, positions);
            const result = await predictor.generatePrediction();
            if (!result.prediction) {
                console.log(`⚠️  ${lotteryType.name} 预测结果为空，跳过保存`);
                return;
            }
            await this.savePrediction(lotteryTypeId, nextPeriod, result.prediction);
            console.log(`✅ ${lotteryType.name} 预测生成完成: 期号 ${nextPeriod}, 胆码 ${result.prediction}`);
        }
        catch (error) {
            console.error(`❌ 生成预测失败:`, error);
        }
    }
    async generatePredictionsForAllLotteries() {
        try {
            console.log(`🎯 开始为所有启用彩种生成预测...`);
            const activeLotteries = await this.getActiveLotteries();
            console.log(`📊 找到 ${activeLotteries.length} 个启用的彩种`);
            for (const lottery of activeLotteries) {
                await this.generatePredictionForLottery(lottery.id);
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            console.log(`✅ 所有彩种预测生成完成`);
        }
        catch (error) {
            console.error(`❌ 批量生成预测失败:`, error);
        }
    }
    async getLotteryType(lotteryTypeId) {
        const result = await database_1.AppDataSource.query(`
      SELECT id, code, name, type, status, draw_interval
      FROM lottery_types
      WHERE id = ?
    `, [lotteryTypeId]);
        return result.length > 0 ? result[0] : null;
    }
    async getActiveLotteries() {
        const result = await database_1.AppDataSource.query(`
      SELECT id, code, name, type, status
      FROM lottery_types
      WHERE status = 'active'
      ORDER BY id
    `);
        return result;
    }
    async getLatestResult(lotteryTypeId) {
        const result = await database_1.AppDataSource.query(`
      SELECT period, draw_time, numbers
      FROM lottery_results
      WHERE lottery_type_id = ?
      ORDER BY draw_time DESC
      LIMIT 1
    `, [lotteryTypeId]);
        return result.length > 0 ? result[0] : null;
    }
    async getPrediction(lotteryTypeId, targetPeriod) {
        const result = await database_1.AppDataSource.query(`
      SELECT id, prediction, created_at
      FROM predictions
      WHERE lottery_type_id = ? AND target_period = ?
    `, [lotteryTypeId, targetPeriod]);
        return result.length > 0 ? result[0] : null;
    }
    async savePrediction(lotteryTypeId, targetPeriod, prediction) {
        try {
            await database_1.AppDataSource.query(`
        INSERT INTO predictions (lottery_type_id, target_period, prediction, created_at)
        VALUES (?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE
        prediction = VALUES(prediction),
        created_at = NOW()
      `, [lotteryTypeId, targetPeriod, prediction]);
            console.log(`💾 预测结果已保存: 彩种 ${lotteryTypeId}, 期号 ${targetPeriod}, 胆码 ${prediction}`);
        }
        catch (error) {
            console.error(`❌ 保存预测结果失败:`, error);
            throw error;
        }
    }
    calculateNextPeriod(currentPeriod, lotteryType) {
        try {
            if (lotteryType.draw_interval && lotteryType.draw_interval < 86400) {
                const periodNum = parseInt(currentPeriod);
                return (periodNum + 1).toString();
            }
            if (currentPeriod.length === 7) {
                const year = currentPeriod.substring(0, 4);
                const num = parseInt(currentPeriod.substring(4));
                const nextNum = num + 1;
                return year + nextNum.toString().padStart(3, '0');
            }
            const periodNum = parseInt(currentPeriod);
            return (periodNum + 1).toString();
        }
        catch (error) {
            console.error(`❌ 计算下期期号失败:`, error);
            const periodNum = parseInt(currentPeriod.replace(/\D/g, ''));
            return (periodNum + 1).toString();
        }
    }
    getPositionsByType(type) {
        switch (type) {
            case '3star':
                return 3;
            case '4star':
                return 4;
            case '5star':
                return 5;
            default:
                return 3;
        }
    }
    async getLatestPrediction(lotteryTypeId) {
        try {
            const result = await database_1.AppDataSource.query(`
        SELECT target_period, prediction, created_at
        FROM predictions
        WHERE lottery_type_id = ?
        ORDER BY created_at DESC
        LIMIT 1
      `, [lotteryTypeId]);
            return result.length > 0 ? result[0] : null;
        }
        catch (error) {
            console.error(`❌ 获取最新预测失败:`, error);
            return null;
        }
    }
    async getHistoryPredictions(lotteryTypeId, limit = 10) {
        try {
            const result = await database_1.AppDataSource.query(`
        SELECT target_period, prediction, created_at
        FROM predictions
        WHERE lottery_type_id = ?
        ORDER BY created_at DESC
        LIMIT ?
      `, [lotteryTypeId, limit]);
            return result;
        }
        catch (error) {
            console.error(`❌ 获取历史预测失败:`, error);
            return [];
        }
    }
    async cleanupOldPredictions(daysToKeep = 30) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
            const result = await database_1.AppDataSource.query(`
        DELETE FROM predictions
        WHERE created_at < ?
      `, [cutoffDate]);
            console.log(`🧹 清理完成，删除了 ${result.affectedRows} 条过期预测数据`);
        }
        catch (error) {
            console.error(`❌ 清理预测数据失败:`, error);
        }
    }
    async getPredictionStats() {
        try {
            const totalResult = await database_1.AppDataSource.query(`
        SELECT COUNT(*) as total FROM predictions
      `);
            const todayResult = await database_1.AppDataSource.query(`
        SELECT COUNT(*) as today FROM predictions
        WHERE DATE(created_at) = CURDATE()
      `);
            const lotteryStatsResult = await database_1.AppDataSource.query(`
        SELECT 
          lt.name as lottery_name,
          COUNT(p.id) as prediction_count,
          MAX(p.created_at) as latest_prediction
        FROM lottery_types lt
        LEFT JOIN predictions p ON lt.id = p.lottery_type_id
        WHERE lt.status = 'active'
        GROUP BY lt.id, lt.name
        ORDER BY lt.name
      `);
            return {
                total: totalResult[0].total,
                today: todayResult[0].today,
                byLottery: lotteryStatsResult
            };
        }
        catch (error) {
            console.error(`❌ 获取预测统计失败:`, error);
            return {
                total: 0,
                today: 0,
                byLottery: []
            };
        }
    }
}
exports.PredictionService = PredictionService;
//# sourceMappingURL=PredictionService.js.map