export declare class PredictionService {
    private static instance;
    private constructor();
    static getInstance(): PredictionService;
    generatePredictionForLottery(lotteryTypeId: number): Promise<void>;
    generatePredictionsForAllLotteries(): Promise<void>;
    private getLotteryType;
    private getActiveLotteries;
    private getLatestResult;
    private getPrediction;
    private savePrediction;
    private calculateNextPeriod;
    private getPositionsByType;
    getLatestPrediction(lotteryTypeId: number): Promise<any>;
    getHistoryPredictions(lotteryTypeId: number, limit?: number): Promise<any[]>;
    cleanupOldPredictions(daysToKeep?: number): Promise<void>;
    getPredictionStats(): Promise<any>;
}
//# sourceMappingURL=PredictionService.d.ts.map